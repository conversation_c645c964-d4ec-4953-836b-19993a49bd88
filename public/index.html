<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .video-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: #000;
            border-radius: 8px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        button:hover {
            opacity: 0.8;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .users-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .user-item {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .user-item:last-child {
            border-bottom: none;
        }
        .user-info {
            flex: 1;
        }
        .call-btn {
            padding: 4px 12px;
            font-size: 12px;
            border-radius: 3px;
        }
        .logs {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC Test Client</h1>
        
        <div id="status" class="status disconnected">
            Disconnected
        </div>

        <div class="controls">
            <input type="text" id="roomId" placeholder="Room ID" value="test-room">
            <input type="text" id="userName" placeholder="Your Name" value="User1">
            <button id="connectBtn" class="btn-primary">Connect</button>
            <button id="joinRoomBtn" class="btn-success" disabled>Join Room</button>
            <button id="leaveRoomBtn" class="btn-danger" disabled>Leave Room</button>
            <button id="startVideoBtn" class="btn-secondary" disabled>Start Video</button>
            <button id="stopVideoBtn" class="btn-secondary" disabled>Stop Video</button>
        </div>

        <div class="video-container">
            <div>
                <h3>Local Video</h3>
                <video id="localVideo" autoplay muted></video>
            </div>
            <div>
                <h3>Remote Video</h3>
                <video id="remoteVideo" autoplay></video>
            </div>
        </div>

        <div class="users-list">
            <h3>Room Users</h3>
            <div id="usersList">No users</div>
        </div>

        <div class="container">
            <h3>Logs</h3>
            <div id="logs" class="logs"></div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        class WebRTCClient {
            constructor() {
                this.socket = null;
                this.localStream = null;
                this.peerConnection = null;
                this.userId = null;
                this.roomId = null;
                this.isInRoom = false;
                
                this.initializeElements();
                this.setupEventListeners();
            }

            initializeElements() {
                this.statusEl = document.getElementById('status');
                this.connectBtn = document.getElementById('connectBtn');
                this.joinRoomBtn = document.getElementById('joinRoomBtn');
                this.leaveRoomBtn = document.getElementById('leaveRoomBtn');
                this.startVideoBtn = document.getElementById('startVideoBtn');
                this.stopVideoBtn = document.getElementById('stopVideoBtn');
                this.roomIdInput = document.getElementById('roomId');
                this.userNameInput = document.getElementById('userName');
                this.localVideo = document.getElementById('localVideo');
                this.remoteVideo = document.getElementById('remoteVideo');
                this.usersListEl = document.getElementById('usersList');
                this.logsEl = document.getElementById('logs');
            }

            setupEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.joinRoomBtn.addEventListener('click', () => this.joinRoom());
                this.leaveRoomBtn.addEventListener('click', () => this.leaveRoom());
                this.startVideoBtn.addEventListener('click', () => this.startVideo());
                this.stopVideoBtn.addEventListener('click', () => this.stopVideo());
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.logsEl.innerHTML += `[${timestamp}] ${message}\n`;
                this.logsEl.scrollTop = this.logsEl.scrollHeight;
                console.log(message);
            }

            updateStatus(status, isConnected) {
                this.statusEl.textContent = status;
                this.statusEl.className = `status ${isConnected ? 'connected' : 'disconnected'}`;
            }

            connect() {
                this.log('Connecting to WebRTC server...');
                this.socket = io('/webrtc');

                this.socket.on('connect', () => {
                    this.log('Connected to server');
                    this.updateStatus('Connected', true);
                    this.connectBtn.disabled = true;
                    this.joinRoomBtn.disabled = false;
                    this.startVideoBtn.disabled = false;
                });

                this.socket.on('disconnect', () => {
                    this.log('Disconnected from server');
                    this.updateStatus('Disconnected', false);
                    this.connectBtn.disabled = false;
                    this.joinRoomBtn.disabled = true;
                    this.leaveRoomBtn.disabled = true;
                    this.startVideoBtn.disabled = true;
                    this.stopVideoBtn.disabled = true;
                });

                this.socket.on('user-id', (data) => {
                    this.userId = data.userId;
                    this.log(`Received user ID: ${this.userId}`);
                });

                this.socket.on('room-joined', (data) => {
                    this.log(`Joined room: ${data.roomId}`);
                    this.roomId = data.roomId;
                    this.isInRoom = true;
                    this.joinRoomBtn.disabled = true;
                    this.leaveRoomBtn.disabled = false;
                    this.updateUsersList(data.users);
                });

                this.socket.on('user-joined', (data) => {
                    this.log(`User joined: ${data.userName} (${data.userId})`);
                });

                this.socket.on('user-left', (data) => {
                    this.log(`User left: ${data.userName} (${data.userId})`);
                });

                this.socket.on('room-users', (data) => {
                    this.updateUsersList(data.users);
                });

                this.socket.on('signaling', (message) => {
                    this.handleSignalingMessage(message);
                });

                this.socket.on('error', (error) => {
                    this.log(`Error: ${error.message}`);
                });
            }

            joinRoom() {
                const roomId = this.roomIdInput.value.trim();
                const userName = this.userNameInput.value.trim();
                
                if (!roomId) {
                    alert('Please enter a room ID');
                    return;
                }

                this.log(`Joining room: ${roomId} as ${userName}`);
                this.socket.emit('join-room', { roomId, userName });
            }

            leaveRoom() {
                if (this.isInRoom) {
                    this.log('Leaving room...');
                    this.socket.emit('leave-room');
                    this.isInRoom = false;
                    this.roomId = null;
                    this.joinRoomBtn.disabled = false;
                    this.leaveRoomBtn.disabled = true;
                    this.usersListEl.innerHTML = 'No users';
                }
            }

            async startVideo() {
                try {
                    this.log('Starting video...');
                    this.localStream = await navigator.mediaDevices.getUserMedia({
                        video: true,
                        audio: true
                    });
                    this.localVideo.srcObject = this.localStream;
                    this.startVideoBtn.disabled = true;
                    this.stopVideoBtn.disabled = false;
                    this.log('Video started successfully');

                    // 如果已经有peer connection，添加新的tracks
                    if (this.peerConnection) {
                        this.localStream.getTracks().forEach(track => {
                            this.peerConnection.addTrack(track, this.localStream);
                        });
                        this.log('Added tracks to existing peer connection');
                    }
                } catch (error) {
                    this.log(`Error starting video: ${error.message}`);
                }
            }

            stopVideo() {
                if (this.localStream) {
                    this.localStream.getTracks().forEach(track => track.stop());
                    this.localStream = null;
                    this.localVideo.srcObject = null;
                    this.startVideoBtn.disabled = false;
                    this.stopVideoBtn.disabled = true;
                    this.log('Video stopped');
                }

                // 关闭peer connection
                if (this.peerConnection) {
                    this.peerConnection.close();
                    this.peerConnection = null;
                    this.remoteVideo.srcObject = null;
                    this.log('Peer connection closed');
                }
            }

            updateUsersList(users) {
                if (users.length === 0) {
                    this.usersListEl.innerHTML = 'No users';
                    return;
                }

                this.usersListEl.innerHTML = users.map(user => {
                    const isCurrentUser = user.userId === this.userId;
                    const callButton = isCurrentUser ? '' :
                        `<button class="btn-primary call-btn" onclick="webrtcClient.createOffer('${user.userId}')">Call</button>`;

                    return `<div class="user-item">
                        <div class="user-info">
                            ${user.userName} (${user.userId.substring(0, 8)}...)
                            ${isCurrentUser ? '(You)' : ''}
                        </div>
                        ${callButton}
                    </div>`;
                }).join('');
            }

            async handleSignalingMessage(message) {
                this.log(`Received signaling: ${message.type} from ${message.fromUserId}`);

                try {
                    switch (message.type) {
                        case 'offer':
                            await this.handleOffer(message.data, message.fromUserId);
                            break;
                        case 'answer':
                            await this.handleAnswer(message.data);
                            break;
                        case 'ice-candidate':
                            await this.handleIceCandidate(message.data);
                            break;
                        default:
                            this.log(`Unknown signaling type: ${message.type}`);
                    }
                } catch (error) {
                    this.log(`Error handling signaling: ${error.message}`);
                }
            }

            async createPeerConnection() {
                if (this.peerConnection) {
                    this.peerConnection.close();
                }

                // 获取ICE服务器配置
                const response = await fetch('/webrtc/ice-servers');
                const iceConfig = await response.json();

                this.peerConnection = new RTCPeerConnection(iceConfig);

                // 处理ICE候选
                this.peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        this.socket.emit('signaling', {
                            type: 'ice-candidate',
                            data: event.candidate,
                            fromUserId: this.userId
                        });
                        this.log('Sent ICE candidate');
                    }
                };

                // 处理远程流
                this.peerConnection.ontrack = (event) => {
                    this.log('Received remote stream');
                    this.remoteVideo.srcObject = event.streams[0];
                };

                // 连接状态变化
                this.peerConnection.onconnectionstatechange = () => {
                    this.log(`Connection state: ${this.peerConnection.connectionState}`);
                };

                // 添加本地流到peer connection
                if (this.localStream) {
                    this.localStream.getTracks().forEach(track => {
                        this.peerConnection.addTrack(track, this.localStream);
                    });
                }

                return this.peerConnection;
            }

            async handleOffer(offer, fromUserId) {
                this.log(`Handling offer from ${fromUserId}`);

                await this.createPeerConnection();
                await this.peerConnection.setRemoteDescription(offer);

                const answer = await this.peerConnection.createAnswer();
                await this.peerConnection.setLocalDescription(answer);

                this.socket.emit('signaling', {
                    type: 'answer',
                    data: answer,
                    targetUserId: fromUserId,
                    fromUserId: this.userId
                });

                this.log('Sent answer');
            }

            async handleAnswer(answer) {
                this.log('Handling answer');

                if (this.peerConnection) {
                    await this.peerConnection.setRemoteDescription(answer);
                    this.log('Set remote description (answer)');
                }
            }

            async handleIceCandidate(candidate) {
                this.log('Handling ICE candidate');

                if (this.peerConnection) {
                    await this.peerConnection.addIceCandidate(candidate);
                    this.log('Added ICE candidate');
                }
            }

            async createOffer(targetUserId) {
                this.log(`Creating offer for ${targetUserId}`);

                await this.createPeerConnection();

                const offer = await this.peerConnection.createOffer();
                await this.peerConnection.setLocalDescription(offer);

                this.socket.emit('signaling', {
                    type: 'offer',
                    data: offer,
                    targetUserId: targetUserId,
                    fromUserId: this.userId
                });

                this.log('Sent offer');
            }
        }

        // Initialize the WebRTC client when the page loads
        let webrtcClient;
        window.addEventListener('load', () => {
            webrtcClient = new WebRTCClient();
        });
    </script>
</body>
</html>
