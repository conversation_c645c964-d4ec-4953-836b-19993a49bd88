import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { networkInterfaces } from 'os';

function getNetworkAddresses() {
  const interfaces = networkInterfaces();
  const addresses = [];

  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      // 跳过内部地址和IPv6地址
      if (iface.family === 'IPv4' && !iface.internal) {
        addresses.push({
          name,
          address: iface.address,
          netmask: iface.netmask,
        });
      }
    }
  }

  return addresses;
}

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // 启用CORS
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // 配置静态文件服务
  app.useStaticAssets(join(__dirname, '..', 'public'));

  const port = process.env.PORT || 3000;
  const host = process.env.HOST || '0.0.0.0';

  await app.listen(port, host);

  const logger = new Logger('Bootstrap');
  logger.log('🚀 WebRTC Server started successfully!');
  logger.log('');

  // 打印本地访问地址
  logger.log('📍 Local access:');
  logger.log(`   http://localhost:${port}`);
  logger.log(`   http://127.0.0.1:${port}`);
  logger.log('');

  // 打印网络访问地址
  const networkAddresses = getNetworkAddresses();
  if (networkAddresses.length > 0) {
    logger.log('🌐 Network access:');
    networkAddresses.forEach(({ name, address }) => {
      logger.log(`   http://${address}:${port} (${name})`);
    });
    logger.log('');
  }

  // 打印WebSocket端点
  logger.log('🔌 WebSocket endpoints:');
  logger.log(`   ws://localhost:${port}/webrtc`);
  networkAddresses.forEach(({ address }) => {
    logger.log(`   ws://${address}:${port}/webrtc`);
  });
  logger.log('');

  // 打印API端点
  logger.log('🔗 API endpoints:');
  logger.log(`   GET  /webrtc/health - Health check`);
  logger.log(`   GET  /webrtc/ice-servers - ICE servers config`);
  logger.log(`   GET  /webrtc/config - WebRTC configuration`);
  logger.log(`   POST /webrtc/generate-room - Generate room ID`);
  logger.log('');

  logger.log('✨ Ready for WebRTC connections!');
}
bootstrap();
