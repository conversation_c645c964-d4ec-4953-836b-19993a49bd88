import { Controller, Get, Post, Body, Param, HttpException, HttpStatus } from '@nestjs/common';
import { WebRtcService } from './webrtc.service';

@Controller('webrtc')
export class WebRtcController {
  constructor(private readonly webrtcService: WebRtcService) {}

  /**
   * 获取ICE服务器配置
   */
  @Get('ice-servers')
  getIceServers() {
    return this.webrtcService.getIceServers();
  }

  /**
   * 获取服务器统计信息
   */
  @Get('stats')
  getServerStats() {
    return this.webrtcService.getServerStats();
  }

  /**
   * 生成新的房间ID
   */
  @Post('generate-room')
  generateRoom() {
    const roomId = this.webrtcService.generateRoomId();
    return {
      roomId,
      message: 'Room ID generated successfully',
    };
  }

  /**
   * 验证房间ID
   */
  @Post('validate-room/:roomId')
  validateRoom(@Param('roomId') roomId: string) {
    const isValid = this.webrtcService.validateRoomId(roomId);
    
    if (!isValid) {
      throw new HttpException(
        'Invalid room ID format. Room ID should be 3-50 characters long and contain only letters, numbers, hyphens, and underscores.',
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      valid: true,
      roomId,
      message: 'Room ID is valid',
    };
  }

  /**
   * 验证用户名
   */
  @Post('validate-username')
  validateUsername(@Body() body: { userName: string }) {
    const { userName } = body;
    
    if (!userName) {
      throw new HttpException('Username is required', HttpStatus.BAD_REQUEST);
    }

    const isValid = this.webrtcService.validateUserName(userName);
    
    if (!isValid) {
      throw new HttpException(
        'Invalid username format. Username should be 1-30 characters long and not contain special characters.',
        HttpStatus.BAD_REQUEST,
      );
    }

    const sanitizedName = this.webrtcService.sanitizeUserName(userName);

    return {
      valid: true,
      originalName: userName,
      sanitizedName,
      message: 'Username is valid',
    };
  }

  /**
   * 健康检查端点
   */
  @Get('health')
  healthCheck() {
    return {
      status: 'ok',
      service: 'WebRTC Server',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  /**
   * 获取WebRTC配置信息
   */
  @Get('config')
  getWebRtcConfig() {
    return {
      maxUsersPerRoom: 10, // 可配置的最大用户数
      supportedCodecs: ['VP8', 'VP9', 'H264', 'AV1'],
      features: {
        audioEnabled: true,
        videoEnabled: true,
        screenShareEnabled: true,
        chatEnabled: true,
      },
      constraints: {
        video: {
          width: { min: 320, ideal: 1280, max: 1920 },
          height: { min: 240, ideal: 720, max: 1080 },
          frameRate: { min: 15, ideal: 30, max: 60 },
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      },
    };
  }
}
