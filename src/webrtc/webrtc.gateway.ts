import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

interface User {
  id: string;
  socketId: string;
  roomId?: string;
  name?: string;
}

interface Room {
  id: string;
  name: string;
  users: Map<string, User>;
  createdAt: Date;
}

interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate';
  data: any;
  targetUserId?: string;
  fromUserId: string;
}

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
  namespace: '/webrtc',
})
export class WebRtcGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebRtcGateway.name);
  private users = new Map<string, User>();
  private rooms = new Map<string, Room>();

  handleConnection(client: Socket) {
    const userId = uuidv4();
    const user: User = {
      id: userId,
      socketId: client.id,
    };

    this.users.set(client.id, user);
    this.logger.log(`Client connected: ${client.id}, User ID: ${userId}`);

    // 发送用户ID给客户端
    client.emit('user-id', { userId });
  }

  handleDisconnect(client: Socket) {
    const user = this.users.get(client.id);
    if (user) {
      // 如果用户在房间中，从房间移除
      if (user.roomId) {
        this.leaveRoom(client, user.roomId);
      }
      this.users.delete(client.id);
      this.logger.log(`Client disconnected: ${client.id}`);
    }
  }

  @SubscribeMessage('join-room')
  handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { roomId: string; userName?: string },
  ) {
    const user = this.users.get(client.id);
    if (!user) {
      client.emit('error', { message: 'User not found' });
      return;
    }

    // 如果用户已经在其他房间，先离开
    if (user.roomId) {
      this.leaveRoom(client, user.roomId);
    }

    const { roomId, userName } = data;
    let room = this.rooms.get(roomId);

    // 如果房间不存在，创建新房间
    if (!room) {
      room = {
        id: roomId,
        name: `Room ${roomId}`,
        users: new Map(),
        createdAt: new Date(),
      };
      this.rooms.set(roomId, room);
      this.logger.log(`Created new room: ${roomId}`);
    }

    // 更新用户信息
    user.roomId = roomId;
    user.name = userName || `User ${user.id.substring(0, 8)}`;

    // 将用户添加到房间
    room.users.set(user.id, user);
    client.join(roomId);

    this.logger.log(`User ${user.id} joined room ${roomId}`);

    // 通知房间内其他用户
    client.to(roomId).emit('user-joined', {
      userId: user.id,
      userName: user.name,
    });

    // 发送房间内现有用户列表给新加入的用户
    const roomUsers = Array.from(room.users.values()).map(u => ({
      userId: u.id,
      userName: u.name,
    }));

    client.emit('room-joined', {
      roomId,
      users: roomUsers,
    });

    // 发送更新的用户列表给房间内所有用户
    this.server.to(roomId).emit('room-users', { users: roomUsers });
  }

  @SubscribeMessage('leave-room')
  handleLeaveRoom(@ConnectedSocket() client: Socket) {
    const user = this.users.get(client.id);
    if (user && user.roomId) {
      this.leaveRoom(client, user.roomId);
    }
  }

  @SubscribeMessage('signaling')
  handleSignaling(
    @ConnectedSocket() client: Socket,
    @MessageBody() message: SignalingMessage,
  ) {
    const user = this.users.get(client.id);
    if (!user || !user.roomId) {
      client.emit('error', { message: 'User not in a room' });
      return;
    }

    const room = this.rooms.get(user.roomId);
    if (!room) {
      client.emit('error', { message: 'Room not found' });
      return;
    }

    // 如果指定了目标用户，只发送给该用户
    if (message.targetUserId) {
      const targetUser = room.users.get(message.targetUserId);
      if (targetUser) {
        this.server.to(targetUser.socketId).emit('signaling', {
          ...message,
          fromUserId: user.id,
        });
      }
    } else {
      // 否则广播给房间内其他所有用户
      client.to(user.roomId).emit('signaling', {
        ...message,
        fromUserId: user.id,
      });
    }

    this.logger.log(
      `Signaling message from ${user.id}: ${message.type} ${
        message.targetUserId ? `to ${message.targetUserId}` : '(broadcast)'
      }`,
    );
  }

  @SubscribeMessage('get-rooms')
  handleGetRooms(@ConnectedSocket() client: Socket) {
    const roomList = Array.from(this.rooms.values()).map(room => ({
      id: room.id,
      name: room.name,
      userCount: room.users.size,
      createdAt: room.createdAt,
    }));

    client.emit('rooms-list', { rooms: roomList });
  }

  private leaveRoom(client: Socket, roomId: string) {
    const user = this.users.get(client.id);
    const room = this.rooms.get(roomId);

    if (user && room) {
      // 从房间移除用户
      room.users.delete(user.id);
      client.leave(roomId);

      // 通知房间内其他用户
      client.to(roomId).emit('user-left', {
        userId: user.id,
        userName: user.name,
      });

      // 更新用户信息
      user.roomId = undefined;

      this.logger.log(`User ${user.id} left room ${roomId}`);

      // 如果房间为空，删除房间
      if (room.users.size === 0) {
        this.rooms.delete(roomId);
        this.logger.log(`Deleted empty room: ${roomId}`);
      } else {
        // 发送更新的用户列表给房间内剩余用户
        const roomUsers = Array.from(room.users.values()).map(u => ({
          userId: u.id,
          userName: u.name,
        }));
        this.server.to(roomId).emit('room-users', { users: roomUsers });
      }
    }
  }
}
