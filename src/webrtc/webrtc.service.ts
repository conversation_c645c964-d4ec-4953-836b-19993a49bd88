import { Injectable, Logger } from '@nestjs/common';

export interface RoomStats {
  id: string;
  name: string;
  userCount: number;
  createdAt: Date;
  lastActivity: Date;
}

export interface ServerStats {
  totalRooms: number;
  totalUsers: number;
  uptime: number;
  startTime: Date;
}

@Injectable()
export class WebRtcService {
  private readonly logger = new Logger(WebRtcService.name);
  private readonly startTime = new Date();

  constructor() {
    this.logger.log('WebRTC Service initialized');
  }

  /**
   * 获取服务器统计信息
   */
  getServerStats(): ServerStats {
    return {
      totalRooms: 0, // 这些数据应该从Gateway获取
      totalUsers: 0,
      uptime: Date.now() - this.startTime.getTime(),
      startTime: this.startTime,
    };
  }

  /**
   * 生成STUN/TURN服务器配置
   */
  getIceServers() {
    return {
      iceServers: [
        {
          urls: 'stun:stun.l.google.com:19302',
        },
        {
          urls: 'stun:stun1.l.google.com:19302',
        },
        // 如果有TURN服务器，可以在这里添加
        // {
        //   urls: 'turn:your-turn-server.com:3478',
        //   username: 'username',
        //   credential: 'password',
        // },
      ],
    };
  }

  /**
   * 验证房间ID格式
   */
  validateRoomId(roomId: string): boolean {
    // 房间ID应该是字母数字组合，长度在3-50之间
    const roomIdRegex = /^[a-zA-Z0-9-_]{3,50}$/;
    return roomIdRegex.test(roomId);
  }

  /**
   * 验证用户名格式
   */
  validateUserName(userName: string): boolean {
    // 用户名长度在1-30之间，允许字母、数字、空格、中文
    if (!userName || userName.length < 1 || userName.length > 30) {
      return false;
    }
    // 检查是否包含不允许的字符
    const invalidChars = /[<>\"'&]/;
    return !invalidChars.test(userName);
  }

  /**
   * 生成随机房间ID
   */
  generateRoomId(): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 清理房间名称
   */
  sanitizeRoomName(name: string): string {
    if (!name) return 'Unnamed Room';
    
    // 移除HTML标签和特殊字符
    const cleaned = name
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/[<>\"'&]/g, '') // 移除危险字符
      .trim();
    
    return cleaned.length > 0 ? cleaned.substring(0, 50) : 'Unnamed Room';
  }

  /**
   * 清理用户名
   */
  sanitizeUserName(name: string): string {
    if (!name) return 'Anonymous';
    
    const cleaned = name
      .replace(/<[^>]*>/g, '')
      .replace(/[<>\"'&]/g, '')
      .trim();
    
    return cleaned.length > 0 ? cleaned.substring(0, 30) : 'Anonymous';
  }

  /**
   * 检查WebRTC配置是否有效
   */
  validateWebRtcConfig(config: any): boolean {
    try {
      // 基本的WebRTC配置验证
      if (!config || typeof config !== 'object') {
        return false;
      }

      // 检查ICE服务器配置
      if (config.iceServers && Array.isArray(config.iceServers)) {
        for (const server of config.iceServers) {
          if (!server.urls) {
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      this.logger.error('WebRTC config validation error:', error);
      return false;
    }
  }

  /**
   * 记录连接事件
   */
  logConnectionEvent(event: string, userId: string, roomId?: string, details?: any) {
    const logData = {
      event,
      userId,
      roomId,
      timestamp: new Date().toISOString(),
      details,
    };
    
    this.logger.log(`Connection Event: ${JSON.stringify(logData)}`);
  }

  /**
   * 记录信令事件
   */
  logSignalingEvent(type: string, fromUserId: string, toUserId?: string, roomId?: string) {
    const logData = {
      type: 'signaling',
      signalingType: type,
      fromUserId,
      toUserId,
      roomId,
      timestamp: new Date().toISOString(),
    };
    
    this.logger.log(`Signaling Event: ${JSON.stringify(logData)}`);
  }
}
