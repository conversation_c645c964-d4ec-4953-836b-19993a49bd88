<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="200" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm run start:dev
```

服务器将在 `http://localhost:3000` 启动。

### 访问测试客户端

打开浏览器访问 `http://localhost:3000` 查看测试客户端界面。

## API 端点

### REST API

- `GET /webrtc/health` - 健康检查
- `GET /webrtc/ice-servers` - 获取 ICE 服务器配置
- `GET /webrtc/stats` - 获取服务器统计信息
- `GET /webrtc/config` - 获取 WebRTC 配置
- `POST /webrtc/generate-room` - 生成新房间ID
- `POST /webrtc/validate-room/:roomId` - 验证房间ID
- `POST /webrtc/validate-username` - 验证用户名

### WebSocket 事件

#### 客户端发送的事件

- `join-room` - 加入房间
  ```json
  {
    "roomId": "room-123",
    "userName": "用户名"
  }
  ```

- `leave-room` - 离开房间

- `signaling` - 发送信令消息
  ```json
  {
    "type": "offer|answer|ice-candidate",
    "data": "WebRTC数据",
    "targetUserId": "目标用户ID（可选）"
  }
  ```

- `get-rooms` - 获取房间列表

#### 服务器发送的事件

- `user-id` - 用户ID分配
- `room-joined` - 成功加入房间
- `user-joined` - 有用户加入房间
- `user-left` - 有用户离开房间
- `room-users` - 房间用户列表更新
- `signaling` - 信令消息
- `rooms-list` - 房间列表
- `error` - 错误消息

## 项目结构

```
src/
├── webrtc/
│   ├── webrtc.gateway.ts    # WebSocket 网关
│   ├── webrtc.service.ts    # 业务逻辑服务
│   ├── webrtc.controller.ts # REST API 控制器
│   └── webrtc.module.ts     # WebRTC 模块
├── app.module.ts            # 主应用模块
└── main.ts                  # 应用入口

public/
└── index.html               # 测试客户端
```

## 使用示例

### 基本连接流程

1. 客户端连接到 WebSocket 端点 `/webrtc`
2. 服务器分配用户ID
3. 客户端加入房间
4. 开始 WebRTC 信令交换

### JavaScript 客户端示例

```javascript
// 连接到 WebRTC 服务器
const socket = io('/webrtc');

// 监听用户ID分配
socket.on('user-id', (data) => {
  console.log('User ID:', data.userId);
});

// 加入房间
socket.emit('join-room', {
  roomId: 'my-room',
  userName: 'John Doe'
});

// 监听房间加入成功
socket.on('room-joined', (data) => {
  console.log('Joined room:', data.roomId);
  console.log('Users in room:', data.users);
});

// 发送信令消息
socket.emit('signaling', {
  type: 'offer',
  data: rtcOffer,
  targetUserId: 'target-user-id'
});

// 监听信令消息
socket.on('signaling', (message) => {
  console.log('Received signaling:', message);
});
```

## 配置

### 环境变量

- `PORT` - 服务器端口（默认: 3000）

### ICE 服务器配置

默认使用 Google 的 STUN 服务器。如需配置 TURN 服务器，请修改 `webrtc.service.ts` 中的 `getIceServers()` 方法。

## 开发

### 运行测试

```bash
pnpm run test
```

### 构建项目

```bash
pnpm run build
```

### 生产环境运行

```bash
pnpm run start:prod
```

## 部署注意事项

1. **HTTPS**: 生产环境中 WebRTC 需要 HTTPS
2. **TURN 服务器**: 对于 NAT 后的客户端，需要配置 TURN 服务器
3. **防火墙**: 确保 WebSocket 端口可访问
4. **负载均衡**: 使用 sticky sessions 确保 WebSocket 连接稳定

## 许可证

本项目采用 MIT 许可证。
